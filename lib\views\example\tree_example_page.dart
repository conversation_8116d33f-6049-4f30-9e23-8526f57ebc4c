import 'package:flutter/material.dart';
import 'package:octasync_client/components/tree/app_tree.dart';
import 'package:octasync_client/components/tree/tree_node_data.dart';
import 'package:octasync_client/components/tree/app_tree_style.dart';

/// 示例数据模型
class ExampleTreeData implements TreeNodeData {
  final String _id;
  final String _name;
  final String? _parentId;
  final List<String> _parentIdList;

  ExampleTreeData({
    required String id,
    required String name,
    String? parentId,
    List<String>? parentIdList,
  }) : _id = id,
       _name = name,
       _parentId = parentId,
       _parentIdList = parentIdList ?? [];

  @override
  String get id => _id;

  @override
  String get name => _name;

  @override
  String? get parentId => _parentId;

  @override
  List<String> get parentIdList => _parentIdList;
}

/// 树组件示例页面
class TreeExamplePage extends StatefulWidget {
  const TreeExamplePage({super.key});

  @override
  State<TreeExamplePage> createState() => _TreeExamplePageState();
}

class _TreeExamplePageState extends State<TreeExamplePage> {
  List<AppTreeNode<ExampleTreeData>> _treeNodes = [];
  String _searchQuery = '';
  bool _showCheckbox = false;
  bool _isLoading = false;
  String? _errorMessage;
  final List<ExampleTreeData> _selectedNodes = [];

  @override
  void initState() {
    super.initState();
    _loadSampleData();
  }

  /// 加载示例数据
  void _loadSampleData() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // 模拟异步加载
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _treeNodes = _buildSampleTree();
          _isLoading = false;
        });
      }
    });
  }

  /// 构建示例树数据
  List<AppTreeNode<ExampleTreeData>> _buildSampleTree() {
    // 创建示例数据
    final sampleData = [
      ExampleTreeData(id: '1', name: '根节点 1', parentIdList: []),
      ExampleTreeData(id: '1-1', name: '子节点 1-1', parentId: '1', parentIdList: ['1']),
      ExampleTreeData(id: '1-2', name: '子节点 1-2', parentId: '1', parentIdList: ['1']),
      ExampleTreeData(id: '1-1-1', name: '孙节点 1-1-1', parentId: '1-1', parentIdList: ['1', '1-1']),
      ExampleTreeData(id: '1-1-2', name: '孙节点 1-1-2', parentId: '1-1', parentIdList: ['1', '1-1']),
      ExampleTreeData(id: '1-2-1', name: '孙节点 1-2-1', parentId: '1-2', parentIdList: ['1', '1-2']),

      ExampleTreeData(id: '2', name: '根节点 2', parentIdList: []),
      ExampleTreeData(id: '2-1', name: '子节点 2-1', parentId: '2', parentIdList: ['2']),
      ExampleTreeData(id: '2-2', name: '子节点 2-2', parentId: '2', parentIdList: ['2']),
      ExampleTreeData(id: '2-1-1', name: '孙节点 2-1-1', parentId: '2-1', parentIdList: ['2', '2-1']),

      ExampleTreeData(id: '3', name: '根节点 3', parentIdList: []),
      ExampleTreeData(id: '3-1', name: '子节点 3-1', parentId: '3', parentIdList: ['3']),
    ];

    return _buildTreeFromData(sampleData);
  }

  /// 从数据列表构建树结构
  List<AppTreeNode<ExampleTreeData>> _buildTreeFromData(List<ExampleTreeData> data) {
    final nodeMap = <String, AppTreeNode<ExampleTreeData>>{};
    final rootNodes = <AppTreeNode<ExampleTreeData>>[];

    // 第一遍：创建所有节点
    for (final item in data) {
      final node = AppTreeNode<ExampleTreeData>(data: item);
      nodeMap[item.id] = node;
    }

    // 第二遍：构建父子关系
    for (final item in data) {
      final currentNode = nodeMap[item.id]!;

      if (item.parentId?.isNotEmpty == true) {
        // 子节点：添加到父节点
        final parentNode = nodeMap[item.parentId!];
        parentNode?.children.add(currentNode);
      } else {
        // 根节点
        rootNodes.add(currentNode);
      }
    }

    return rootNodes;
  }

  /// 模拟加载错误
  void _simulateError() {
    setState(() {
      _isLoading = false;
      _errorMessage = '加载数据时发生错误，请重试';
      _treeNodes = [];
    });
  }

  /// 重试加载
  void _retryLoad() {
    _loadSampleData();
  }

  /// 处理节点点击
  void _handleNodeTap(ExampleTreeData data) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('点击了节点: ${data.name}')));
  }

  /// 处理节点选择
  void _handleNodeSelected(ExampleTreeData data, bool isSelected) {
    setState(() {
      if (isSelected) {
        if (!_selectedNodes.any((node) => node.id == data.id)) {
          _selectedNodes.add(data);
        }
      } else {
        _selectedNodes.removeWhere((node) => node.id == data.id);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('树组件示例'), automaticallyImplyLeading: false),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildControlPanel(),
            const SizedBox(height: 16),
            _buildSelectedInfo(),
            const SizedBox(height: 16),
            Expanded(child: _buildTreeView()),
          ],
        ),
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('控制面板', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: '搜索节点',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Row(
                  children: [
                    Checkbox(
                      value: _showCheckbox,
                      onChanged: (value) {
                        setState(() {
                          _showCheckbox = value ?? false;
                        });
                      },
                    ),
                    const Text('显示复选框'),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                ElevatedButton(onPressed: _loadSampleData, child: const Text('重新加载')),
                const SizedBox(width: 8),
                ElevatedButton(onPressed: _simulateError, child: const Text('模拟错误')),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _treeNodes = [];
                    });
                  },
                  child: const Text('清空数据'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建选中信息
  Widget _buildSelectedInfo() {
    if (!_showCheckbox || _selectedNodes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('已选中节点 (${_selectedNodes.length})', style: Theme.of(context).textTheme.titleSmall),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children:
                  _selectedNodes
                      .map(
                        (node) => Chip(
                          label: Text(node.name),
                          onDeleted: () => _handleNodeSelected(node, false),
                        ),
                      )
                      .toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建树视图
  Widget _buildTreeView() {
    return Card(
      child: AppTree<ExampleTreeData>(
        nodes: _treeNodes,
        showCheckbox: _showCheckbox,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        onNodeTap: _handleNodeTap,
        onNodeSelected: _handleNodeSelected,
        isLoading: _isLoading,
        errorMessage: _errorMessage,
        onRetry: _retryLoad,
        style: AppTreeStyle.defaultStyle(),
      ),
    );
  }
}
