# 树组件 (AppTree)

通用的树形结构组件，支持任何实现了 `TreeNodeData` 接口的数据类型。

## 特性

- ✅ 支持任意数据类型（通过 `TreeNodeData` 接口）
- ✅ 可选的复选框支持（单选/多选）
- ✅ 搜索过滤功能
- ✅ 自动展开第一个父节点
- ✅ 可自定义样式
- ✅ 加载状态、错误状态、空数据状态
- ✅ 性能优化（缓存、节点映射）
- ✅ 完整的测试覆盖

## 基本用法

### 1. 实现数据接口

```dart
class MyTreeData implements TreeNodeData {
  final String _id;
  final String _name;
  final String? _parentId;
  final List<String> _parentIdList;

  MyTreeData({
    required String id,
    required String name,
    String? parentId,
    List<String>? parentIdList,
  }) : _id = id,
       _name = name,
       _parentId = parentId,
       _parentIdList = parentIdList ?? [];

  @override
  String get id => _id;

  @override
  String get name => _name;

  @override
  String? get parentId => _parentId;

  @override
  List<String> get parentIdList => _parentIdList;
}
```

### 2. 构建树节点

```dart
List<AppTreeNode<MyTreeData>> buildTree(List<MyTreeData> data) {
  final nodeMap = <String, AppTreeNode<MyTreeData>>{};
  final rootNodes = <AppTreeNode<MyTreeData>>[];

  // 第一遍：创建所有节点
  for (final item in data) {
    final node = AppTreeNode<MyTreeData>(data: item);
    nodeMap[item.id] = node;
  }

  // 第二遍：构建父子关系
  for (final item in data) {
    final currentNode = nodeMap[item.id]!;
    
    if (item.parentId?.isNotEmpty == true) {
      // 子节点：添加到父节点
      final parentNode = nodeMap[item.parentId!];
      parentNode?.children.add(currentNode);
    } else {
      // 根节点
      rootNodes.add(currentNode);
    }
  }

  return rootNodes;
}
```

### 3. 使用树组件

```dart
AppTree<MyTreeData>(
  nodes: treeNodes,
  showCheckbox: true,
  searchQuery: searchText,
  onNodeTap: (data) {
    print('点击了节点: ${data.name}');
  },
  onNodeSelected: (data, isSelected) {
    print('节点 ${data.name} 选择状态: $isSelected');
  },
  style: AppTreeStyle.defaultStyle(),
)
```

## 高级用法

### 自定义样式

```dart
AppTree<MyTreeData>(
  nodes: treeNodes,
  style: AppTreeStyle.defaultStyle().copyWith(
    nodeHeight: 40.0,
    indentPerLevel: 24.0,
    nodeBorderRadius: BorderRadius.circular(8.0),
  ),
)
```

### 处理状态

```dart
AppTree<MyTreeData>(
  nodes: treeNodes,
  isLoading: isLoading,
  errorMessage: errorMessage,
  onRetry: () {
    // 重试逻辑
  },
  emptyDataMessage: '暂无数据',
  loadingMessage: '正在加载...',
)
```

## API 参考

### AppTree 属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `nodes` | `List<AppTreeNode<T>>` | 必需 | 树节点数据列表 |
| `showCheckbox` | `bool` | `false` | 是否显示复选框 |
| `searchQuery` | `String?` | `null` | 搜索查询字符串 |
| `onNodeTap` | `Function(T)?` | `null` | 节点点击回调 |
| `onNodeSelected` | `Function(T, bool)?` | `null` | 节点选择回调 |
| `style` | `AppTreeStyle` | 默认样式 | 树组件样式配置 |
| `isLoading` | `bool` | `false` | 加载状态 |
| `errorMessage` | `String?` | `null` | 错误信息 |
| `onRetry` | `VoidCallback?` | `null` | 重试回调 |

### TreeNodeData 接口

| 属性 | 类型 | 描述 |
|------|------|------|
| `id` | `String?` | 节点唯一标识符 |
| `name` | `String` | 节点显示名称 |
| `parentId` | `String?` | 父节点ID |
| `parentIdList` | `List<String>` | 父节点ID列表（用于计算层级） |

## 示例

查看 `lib/views/example/tree_example_page.dart` 获取完整的使用示例。

## 测试

运行测试：

```bash
flutter test test/components/tree/app_tree_test.dart
```
